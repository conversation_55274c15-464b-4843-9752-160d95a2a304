# mcap-extractor
此版本工具用于将mcap文件输出为xtreme1格式的数据。
1. 提取MCAP包中的内容。当前支持：
- H264
- YUV NV12图像
- bin格式点云
- pcd点云
- odometry(json)
2. 同步相近时间戳的点云，并将两帧点云合并
3. 同步合并后的点云与h264文件
4. 将bin格式的点云转为pcd格式
5. 分割四合一图像，并对每个摄像头去畸变
6. 输出xtreme1格式的config

输出文件格式如下：
```
# livox + hasco:
.
├── stage_0_mcap_content
│   ├── combined_bin
│   ├── h264
│   ├── hasco_lidar_bin
│   └── livox_lidar_bin
├── stage_1_matched
│   ├── combined_bin
│   └── h264
└── stage_2_release
    ├── camera_config
    ├── camera_image_0
    ├── camera_image_1
    ├── camera_image_2
    ├── camera_image_3
    └── lidar_point_cloud_0


# 4 * hasco
├── stage_0_mcap_content
│   ├── h264
│   │   └── frames
│   ├── hasco_lidar_pcd
│   └── odometry
├── stage_3_matched
│   ├── h264
│   ├── odometry
│   └── pcd
├── stage_4_release
│   ├── camera_config
│   ├── camera_image_0
│   ├── camera_image_1
│   ├── camera_image_2
│   ├── camera_image_3
│   ├── lidar_point_cloud_0
│   └── odometry
└── upload files
    ├── scene_0
    │   ├── camera_config
    │   ├── camera_image_0
    │   ├── camera_image_1
    │   ├── camera_image_2
    │   ├── camera_image_3
    │   ├── lidar_point_cloud_0
    │   └── odometry
    ├── scene_1
    ├── ...
```

## 安装
**构建容器镜像(recommand)：**
```bash
docker build -t mcap-extractor:latest .
```

## 运行
**stage_0: 解析数据**
``` bash
docker run --rm -t \    
-v ./data/:/data  \
mcap-extractor:latest   \
--input_dir=/data/path_2_mcapfiles \     
--output_dir=/data/output  \
--model_path=/data/param.json  \
--h264_topic="/Data_H264" \
--hasco_topic="/point_cloud_hasco_4lidars"  \
--combine_lidars=True      \
--stage=0
```

**参数说明：**
```
--input_dir: mcap文件所在目录             
--output_dir： 解析成功数据输出目录  
--stage: 数据处理阶段  
- stage0-解析数据，
- stage1-图像/点云(bin)数据对齐(Deprecated)
- stage2-导出xtreme1格式数据(Deprecated)
- stage3-图像/点云(pcd)数据对齐
- stage4-导出xtreme1格式数据
--model(Deprecated): 车辆数据模型(camera/lidar内外参)
--model_path: 车辆数据模型地址
--h264_topic: h264 topic
--image_topic: 图像topic
--zvision_pcd_topic: zvixion_lidar topic,输出pcd格式数据
--livox_pcd_topic: zvixion_lidar topic,输出pcd格式数据
--hasco_pcd_topic: zvixion_lidar topic,输出pcd格式数据
--zvision_topic: zvixion_lidar topic,输出bin格式数据
--livox_topic: livox_lidar topic,输出bin格式数据
--hasco_topic: hasco_lidar topic,输出bin格式数据
--split: 是否分割/Data_Cam_Global图像数据
--undistort: 是否对图像去畸变
--focal_scale： 相机内参的focal_scale
--view_seq: 相机顺序
--verbose
--start_time： 截取数据起始时间
--end_time： 截取数据终止时间
--combine_lidars: 是否合并两个lidar的结果(针对双lidar功能)
```


**stage_1: 同步匹配lidar与h264(deprecated)**
``` bash
docker run --rm -t \    
-v ./data/:/data  \
mcap-extractor   \
--input_dir=/data/path_2_mcapfiles \     
--output_dir=/data/output  \
--model_path=/data/param.json  \
--h264_topic="/Data_H264" \
--hasco_topic="/point_cloud_hasco"  \
--livox_topic="/livox/lidar"      \
--combine_lidars=True      \
--stage=1
```

**stage_2: 输出xtreme1格式的文件(deprecated)**
``` bash
docker run --rm -t \    
-v ./data/:/data  \
mcap-extractor   \
--input_dir=/data/path_2_mcapfiles \     
--output_dir=/data/output  \
--model_path=/data/param.json  \
--h264_topic="/Data_H264" \
--hasco_topic="/point_cloud_hasco"  \
--livox_topic="/livox/lidar"      \
--combine_lidars=True      \
--stage=1
```

**stage_3: 同步匹配 hasco_4lidars & H264**
``` bash
docker run --rm -t \    
-v ./data/:/data  \
mcap-extractor   \
--input_dir=/data/path_2_mcapfiles \     
--output_dir=/data/output  \
--model_path=/data/param.json  \
--h264_topic="/Data_H264" \
--hasco_topic="/point_cloud_hasco"  \
--stage=3
```

**stage_4: hasco_4lidars 输出xtreme1格式的文件**
``` bash
docker run --rm -t \    
-v ./data/:/data  \
mcap-extractor   \
--input_dir=/data/path_2_mcapfiles \     
--output_dir=/data/output  \
--model_path=/data/param_path.json  \
--stage=4
```

**split dataset: **
``` bash
sudo docker run --rm -t  -v ./data/:/data  mcap-extractor  --input_dir=/data/data_folder --output_dir=/data/output  --divide true --worker 16
```
docker run --rm -t  -v $(pwd)/:/data  mcap-extractor  --input_dir=/data/data_folder --output_dir=/data/output  --divide true --worker 16

原生Python环境下：
```bash
pip3 install -r requirements.txt
curl -L http://*************:9000/public/static/mcap_ros2_support-0.5.2-py3-none-any.whl -O
pip install --no-cache-dir mcap_ros2_support-0.5.2-py3-none-any.whl opencv-python-headless av mcap-protobuf-support tqdm pypcd4
rm mcap_ros2_support-0.5.2-py3-none-any.whl
```



## 运行
```bash
python3 main.py \
    --input_dir=/input \
    --output_dir=/output \
    --model=pt1 \
    --undistort=true \
    --focal_scale=1.0 \
    --view_seq="front,rear,left,right" \
    --h264_topic="/Data_H264" \
    --zvision_topic="/zvision_lidar_points" \
    --livox_topic="/livox/lidar"
    --hasco_topic="/point_cloud_hasco"
    --image_topic="/Data_Cam_Global"
    --start_time='1719906176700'
    --end_time='1719906188000'
    --combine_lidars=True
```

参数说明
- `input_dir` mcap文件目录
- `output_dir` mcap解包后存储目录
- `model` 车型，可选`dr1`、`dr2`、`pt1`、`pt2`。
- `undistort` 是否去除畸变 `True`/`False`
- `focal_scale` 去畸变参数，默认`1.0`
- `view_seq` 按照Z字顺序声明摄像头位置，例如`--view_seq="front,rear,left,right"`
- `h264_topic` H264数据Topic，例如`"/Data_H264"`
- `zvision_topic` 激光雷达数据Topic，例如`"/zvision_lidar_points"`
- `livox_topic` 激光雷达数据Topic，例如`"/livox/lidar"`
- `image_topic` 图像数据Topic，例如`"/Data_Cam_Global"`
- `start_time` 开始的时间戳（ms）例如`"1719906188000"`
- `end_time` 结束的时间戳（ms）例如`"1719906189000"`
- `combine_lidars` 合并主副lidar点云

使用Docker
```bash
docker run --rm -t \
    -v ./data:/data \
    192.168.1.130:8085/perception/mcap-extractor:0.2 \
    --input_dir=/data \
    --output_dir=/data \
    --model=pt1 \
    --undistort=True \
    --focal_scale=1.0 \
    --view_seq="front,rear,left,right" \
    --h264_topic="/Data_H264" \
    --zvision_topic="/zvision_lidar_points" \
    --livox_topic="/livox/lidar" \
    --image_topic="/Data_Cam_Global" \
    --start_time="1719906188000" \
    --end_time="1719906189000" \
    --combine_lidars=True


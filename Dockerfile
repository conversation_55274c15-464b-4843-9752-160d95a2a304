FROM ros:humble-ros-base-jammy

RUN apt update && \
    apt install -y --no-install-recommends curl python3-pip libopenblas-dev && \
    apt clean && rm -rf /var/lib/apt/lists/*
COPY mcap_ros2_support-0.5.2-py3-none-any.whl .
#RUN curl -L http://*************:9000/public/static/mcap_ros2_support-0.5.2-py3-none-any.whl -O && \
RUN pip install --no-cache-dir mcap_ros2_support-0.5.2-py3-none-any.whl opencv-python-headless av mcap-protobuf-support tqdm pypcd4
#    rm /workspace/mcap_ros2_support-0.5.2-py3-none-any.whl

WORKDIR /workspace
# ADD *.py /workspace/
COPY . /workspace/

ENTRYPOINT ["python3", "main.py"]

CMD ["--help"]

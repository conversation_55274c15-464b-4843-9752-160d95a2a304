import os
import numpy as np
from tqdm import *
import cv2
import json
from pathlib import Path

from pypcd4 import PointCloud, Encoding

# , DR1, DR2, PT1, PT2, PT3_3, PT3_4
from tools.cam_params import View, VehicleParam, CamParam

import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm  # 导入 tqdm 库


def get_bin_names_from_folder(folder):
    """
    从文件夹中获取文件名
    """
    file_names = []
    for root, dirs, files in os.walk(folder):
        for file in files:
            file_info = file.split(".")
            if file_info[1] == "bin":
                file_names.append(int(file_info[0]))
    return file_names


def get_jpg_names_from_folder(folder):
    """
    从文件夹中获取文件名
    """
    file_names = []
    for root, dirs, files in os.walk(folder):
        for file in files:
            file_info = file.split(".")
            if file_info[1] == "jpg":
                file_names.append(int(file_info[0]))
    return file_names


def get_pcd_names_from_folder(folder):
    """
    从文件夹中获取文件名
    """
    file_names = []
    for root, dirs, files in os.walk(folder):
        for file in files:
            file_info = file.split(".")
            if file_info[1] == "pcd":
                file_names.append(int(file_info[0]))
    return file_names


def get_names_from_folder_by_subfix(folder, subfix="json"):
    """
    从文件夹中获取文件名
    """
    file_names = []
    for root, dirs, files in os.walk(folder):
        for file in files:
            file_info = file.split(".")
            if file_info[1] == subfix:
                file_names.append(int(file_info[0]))
    return file_names


def pos_trans_by_matrix(rotation_angles, translation, point_cloud):
    """
    对点云数据进行旋转和平移。

    参数:
        point_cloud: 点云数据，形状为 (N, 3)。
        rotation_angles: 绕 x、y、z 轴的旋转角度 (单位：度)，形状为 (3,).
        translation: 平移向量，形状为 (3,).

    返回值:
        变换后的点云数据，形状为 (N, 3)。
    """

    # 将角度转换为弧度
    rx, ry, rz = np.radians(rotation_angles)

    # 创建旋转矩阵
    rotation_x = np.array(
        [
            [1, 0, 0, 0],
            [0, np.cos(rx), -np.sin(rx), 0],
            [0, np.sin(rx), np.cos(rx), 0],
            [0, 0, 0, 1],
        ]
    )

    rotation_y = np.array(
        [
            [np.cos(ry), 0, np.sin(ry), 0],
            [0, 1, 0, 0],
            [-np.sin(ry), 0, np.cos(ry), 0],
            [0, 0, 0, 1],
        ]
    )

    rotation_z = np.array(
        [
            [np.cos(rz), -np.sin(rz), 0, 0],
            [np.sin(rz), np.cos(rz), 0, 0],
            [0, 0, 1, 0],
            [0, 0, 0, 1],
        ]
    )

    # 创建平移矩阵
    translation_matrix = np.eye(4)
    translation_matrix[:3, 3] = translation

    # 组合变换矩阵
    transformation_matrix = translation_matrix @ rotation_x @ rotation_y @ rotation_z

    # 将点云数据转换为齐次坐标
    homogeneous_coordinates = np.hstack(
        (point_cloud, np.ones((point_cloud.shape[0], 1)))
    )

    # 应用变换
    transformed_points = (transformation_matrix @ homogeneous_coordinates.T).T

    # 将齐次坐标转换回三维坐标
    return transformed_points[:, :3]


def match_main_and_sub_points(main_folder, sub_folder):

    main_files = get_bin_names_from_folder(main_folder)
    sub_files = get_bin_names_from_folder(sub_folder)

    main_array = np.array(main_files)
    sub_array = np.array(sub_files)

    distance_matrix = np.abs(main_array[:, np.newaxis] - sub_array)
    min_index = np.argmin(distance_matrix, axis=1)

    closest_pairs = [
        (main_array[i], sub_array[min_index[i]]) for i in range(len(main_array))
    ]
    min_distances = distance_matrix[np.arange(len(main_array)), min_index]

    closest_pairs = np.array(closest_pairs)
    min_distances = np.array(min_distances)

    zero_difference_path = closest_pairs[np.where(min_distances == 0)]

    return zero_difference_path


def match_lidar_and_h264(lidar_folder, h264_folder):
    lidar_files = get_bin_names_from_folder(lidar_folder)
    h264_files = get_jpg_names_from_folder(h264_folder)

    lidar_array = np.array(lidar_files)
    h264_array = np.array(h264_files)

    distance_matrix = np.abs(lidar_array[:, np.newaxis] - h264_array)
    min_index = np.argmin(distance_matrix, axis=1)

    closest_pairs = [
        (lidar_array[i], h264_array[min_index[i]]) for i in range(len(lidar_array))
    ]
    min_distances = distance_matrix[np.arange(len(lidar_array)), min_index]

    closest_pairs = np.array(closest_pairs)
    min_distances = np.array(min_distances)

    min_difference_path = closest_pairs[np.where(min_distances < 35)]

    return min_difference_path


def bin_to_pcd(bin_path, pcd_path):

    points = np.fromfile(bin_path, dtype=np.float32).reshape(-1, 4)
    pcd = PointCloud.from_xyzi_points(points)  # 二进制文件包含 x, y, z, intensity

    # 保存为 ASCII 格式的 PCD 文件
    # pcd.save(pcd_path, encoding=PointCloud.Encoding.ASCII)
    pcd.save(pcd_path, encoding=Encoding.ASCII)
    fmt = "%f %f %f %.1f"  # intensity 保留一位小数shape

    # 保存点云数据，使用自定义的格式化字符串
    with open(pcd_path, "w") as f:
        # 写入 PCD 头部信息
        f.write(pcd.metadata.compose_header())
        # 写入点云数据
        np.savetxt(f, points, fmt=fmt)


def bin_to_pcd_patch(bin_folder, pcd_folder):
    # 遍历 bin
    for filename in tqdm(os.listdir(bin_folder)):
        if filename.endswith(".bin"):
            bin_path = os.path.join(bin_folder, filename)
            pcd_filename = filename[:-4] + ".pcd"  # 去掉.bin扩展名，加上.pcd
            pcd_path = os.path.join(pcd_folder, pcd_filename)

            # 转bin 为 PCD 文件
            bin_to_pcd(bin_path, pcd_path)
            # print(f"Converted {bin_path} to {pcd_path}")


def undistort(bgr: np.ndarray, focal_scale: float, param: CamParam):
    # 使用FOCAL_SCALE缩放焦距
    new_camera_matrix = param.cam_matrix.copy()
    new_camera_matrix[0][0] *= focal_scale
    new_camera_matrix[1][1] *= focal_scale

    mapx, mapy = cv2.fisheye.initUndistortRectifyMap(
        param.cam_matrix,
        param.dist_coeffs,
        np.eye(3),
        new_camera_matrix,
        (param.width, param.height),
        cv2.CV_32FC1,
    )
    bgr = cv2.resize(bgr, (param.width, param.height))
    bgr = cv2.remap(
        bgr,
        mapx,
        mapy,
        interpolation=cv2.INTER_LINEAR,
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(0, 0, 0),
    )
    return bgr


def crop_and_undistort(
    h264_folder,
    save_folder,
    model,
    focal_scale=1.0,
    view_seq=[View.FRONT, View.REAR, View.LEFT, View.RIGHT],
):

    print("cropping and undisorting ...")
    for root, dirs, files in os.walk(h264_folder):
        for file in tqdm(files):

            img = cv2.imread(os.path.join(h264_folder, file))
            file_name = file.split(".")[0]
            h, w, _ = img.shape
            h_half, w_half = h // 2, w // 2
            origins = [
                (0, 0),
                (0 + w_half, 0),
                (0, 0 + h_half),
                (0 + w_half, 0 + h_half),
            ]
            for i, (x, y) in enumerate(origins):
                param = model.cam_param(view_seq[i])
                patch = img[y: y + h_half, x: x + w_half]

                patch = undistort(patch, focal_scale, param)
                cv2.imwrite(
                    os.path.join(
                        save_folder, f"camera_image_{i}", f"{file_name}.jpg"),
                    patch
                )


def crop_and_undistort_in_parallel(
    h264_folder,
    save_folder,
    model,
    focal_scale=1.0,
    view_seq=[View.FRONT, View.REAR, View.LEFT, View.RIGHT],
):

    print("cropping and undisorting ...")
    for root, dirs, files in os.walk(h264_folder):
        for file in tqdm(files):

            img = cv2.imread(os.path.join(h264_folder, file))
            file_name = file.split(".")[0]
            h, w, _ = img.shape
            h_half, w_half = h // 2, w // 2
            origins = [
                (0, 0),
                (0 + w_half, 0),
                (0, 0 + h_half),
                (0 + w_half, 0 + h_half),
            ]
            for i, (x, y) in enumerate(origins):
                param = model.cam_param(view_seq[i])
                patch = img[y: y + h_half, x: x + w_half]

                patch = undistort(patch, focal_scale, param)
                cv2.imwrite(
                    os.path.join(
                        save_folder, f"camera_image_{i}", f"{file_name}.jpg"),
                    patch
                )


def export_params_to_dict(model):
    output_dict = [
        {
            "camera_internal": {
                "fx": model.cam_front.cam_matrix[0][0],
                "fy": model.cam_front.cam_matrix[1][1],
                "cx": model.cam_front.cam_matrix[0][2],
                "cy": model.cam_front.cam_matrix[1][2],
            },
            "width": model.cam_front.width,
            "height": model.cam_front.height,
            "camera_external": model.cam_front.external.flatten().tolist(),
            "rowMajor": True,
        },
        {
            "camera_internal": {
                "fx": model.cam_rear.cam_matrix[0][0],
                "fy": model.cam_rear.cam_matrix[1][1],
                "cx": model.cam_rear.cam_matrix[0][2],
                "cy": model.cam_rear.cam_matrix[1][2],
            },
            "width": model.cam_rear.width,
            "height": model.cam_rear.height,
            "camera_external": model.cam_rear.external.flatten().tolist(),
            "rowMajor": True,
        },
        {
            "camera_internal": {
                "fx": model.cam_left.cam_matrix[0][0],
                "fy": model.cam_left.cam_matrix[1][1],
                "cx": model.cam_left.cam_matrix[0][2],
                "cy": model.cam_left.cam_matrix[1][2],
            },
            "width": model.cam_left.width,
            "height": model.cam_left.height,
            "camera_external": model.cam_left.external.flatten().tolist(),
            "rowMajor": True,
        },
        {
            "camera_internal": {
                "fx": model.cam_right.cam_matrix[0][0],
                "fy": model.cam_right.cam_matrix[1][1],
                "cx": model.cam_right.cam_matrix[0][2],
                "cy": model.cam_right.cam_matrix[1][2],
            },
            "width": model.cam_right.width,
            "height": model.cam_right.height,
            "camera_external": model.cam_right.external.flatten().tolist(),
            "rowMajor": True,
        },
    ]
    return output_dict


def check_path(path: Path):
    if not path.exists():
        raise ValueError(f"[error]: {path} does not exits")


def split_timestamp(timestamps: list, interval=100, tolerance=2):
    if len(timestamps) < interval:
        print("[Divider] input files less than the mininum interval")
        return

    print(f"[Divider] get {len(timestamps)} timestamps")
    timestamps.sort()

    timestamp_splits = []
    current_idx = 0

    while current_idx < len(timestamps):
        if current_idx + interval >= len(timestamps):
            break

        diff = timestamps[current_idx + interval] - timestamps[current_idx]
        if diff <= (interval + tolerance) * 100:
            split = timestamps[current_idx: current_idx + interval]
            timestamp_splits.append(split)
            current_idx = current_idx + interval
        else:
            current_idx = current_idx + 1

    print(f"[Divider] get {len(timestamp_splits)} splits")
    return timestamp_splits


def create_scene_folder_with_odometry(folder: Path):
    """
    创建带有odometry的scene文件夹

    :param folder: 文件路径或文件名
    :return: 各个文件夹路径
    """
    camera_config = folder / "camera_config"
    camera_config.mkdir(parents=True, exist_ok=True)
    camera_image_0 = folder / "camera_image_0"
    camera_image_0.mkdir(parents=True, exist_ok=True)
    camera_image_1 = folder / "camera_image_1"
    camera_image_1.mkdir(parents=True, exist_ok=True)
    camera_image_2 = folder / "camera_image_2"
    camera_image_2.mkdir(parents=True, exist_ok=True)
    camera_image_3 = folder / "camera_image_3"
    camera_image_3.mkdir(parents=True, exist_ok=True)
    lidar_point_cloud_0 = folder / "lidar_point_cloud_0"
    lidar_point_cloud_0.mkdir(parents=True, exist_ok=True)
    odometry = folder / "odometry"
    odometry.mkdir(parents=True, exist_ok=True)
    path_dict = {
        "camera_config": camera_config,
        "camera_image_0": camera_image_0,
        "camera_image_1": camera_image_1,
        "camera_image_2": camera_image_2,
        "camera_image_3": camera_image_3,
        "lidar_point_cloud_0": lidar_point_cloud_0,
        "odometry": odometry,
    }

    return path_dict


def get_filename_without_extension(file_path):
    """
    获取文件名（不包含后缀名）。

    :param file_path: 文件路径或文件名
    :return: 文件名（不包含后缀名）
    """
    return os.path.splitext(file_path)[0]


def find_common_files(folders):
    """
    找出所有文件夹中同名的文件（忽略后缀名）。

    :param folders: 包含所有文件夹路径的列表
    :return: 所有文件夹中同名的文件名集合（不包含后缀名）
    """
    if not folders:
        return set()

    # 获取第一个文件夹中的文件名集合（忽略后缀名）
    common_files = set(
        get_filename_without_extension(f) for f in os.listdir(folders[0])
    )

    # 遍历剩余文件夹，取交集
    for folder in folders[1:]:
        current_files = set(
            get_filename_without_extension(f) for f in os.listdir(folder)
        )
        common_files.intersection_update(current_files)

    return common_files


def delete_uncommon_files(folder, common_files):
    """
    删除文件夹中不在共同文件列表中的文件（忽略后缀名）。

    :param folder: 文件夹路径
    :param common_files: 共同文件名集合（不包含后缀名）
    """
    for filename in os.listdir(folder):
        filename_without_ext = get_filename_without_extension(filename)
        if filename_without_ext not in common_files:
            file_path = os.path.join(folder, filename)
            if os.path.isfile(file_path):  # 确保只删除文件，不删除子文件夹
                os.remove(file_path)
                print(f"Deleted: {file_path}")


# def copy_file(src, dst):
#     """
#     复制单个文件，并保留元数据。

#     :param src: 源文件路径
#     :param dst: 目标文件路径
#     """
#     try:
#         # 确保目标目录存在
#         os.makedirs(os.path.dirname(dst), exist_ok=True)
#         # 使用 shutil.copy2 复制文件并保留元数据
#         shutil.copy2(src, dst)
#         print(f"Copied: {src} -> {dst}")
#         return True
#     except Exception as e:
#         print(f"Failed to copy {src} to {dst}: {e}")
#         return False


# def copy_files_in_parallel(src_files, dst_files, max_workers=4):
#     """
#     使用多线程复制文件。

#     :param src_files: 源文件路径列表
#     :param dst_files: 目标文件路径列表
#     :param max_workers: 最大线程数
#     """
#     if len(src_files) != len(dst_files):
#         print("Error: Source and destination file lists must have the same length.")
#         return

#     with ThreadPoolExecutor(max_workers=max_workers) as executor:
#         # 提交任务到线程池
#         futures = [
#             executor.submit(copy_file, src, dst)
#             for src, dst in zip(src_files, dst_files)
#         ]

#         # 等待所有任务完成
#         for future in as_completed(futures):
#             future.result()  # 获取结果（如果有异常会在这里抛出）


def copy_file(src, dst):
    """
    复制单个文件，并保留元数据。

    :param src: 源文件路径
    :param dst: 目标文件路径
    """
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(dst), exist_ok=True)
        # 使用 shutil.copy2 复制文件并保留元数据
        shutil.copy2(src, dst)
        return True, src, dst
    except Exception as e:
        return False, src, dst, str(e)


def copy_files_in_parallel(
    src_files, dst_files, max_workers=4, description="Copying files"
):
    """
    使用多线程复制文件，并显示进度条。

    :param src_files: 源文件路径列表
    :param dst_files: 目标文件路径列表
    :param max_workers: 最大线程数
    """
    if len(src_files) != len(dst_files):
        print("Error: Source and destination file lists must have the same length.")
        return

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交任务到线程池
        futures = [
            executor.submit(copy_file, src, dst)
            for src, dst in zip(src_files, dst_files)
        ]

        # 使用 tqdm 显示进度条
        success_count = 0
        failure_count = 0
        with tqdm(total=len(futures), desc=description, unit="file") as pbar:
            for future in as_completed(futures):
                result = future.result()
                if result[0]:  # 复制成功
                    success_count += 1
                else:  # 复制失败
                    failure_count += 1
                    print(
                        f"Failed to copy {result[1]} to {result[2]}: {result[3]}")
                pbar.update(1)  # 更新进度条

        print(
            f"Copy completed: {success_count} files succeeded, {failure_count} files failed."
        )

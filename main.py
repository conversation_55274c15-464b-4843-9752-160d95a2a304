import os
import sys
import numpy as np
from argparse import ArgumentPars<PERSON>
from pathlib import Path
import shutil
from tqdm import *

from tools.mcap_parser import <PERSON>capParser
from tools.combiner import Combiner, Matcher

# DR1, DR2, PT1, PT2, PT3, PT3_3, PT3_4,
from tools.cam_params import View, parse_json_to_vehicleParam
from utils.utils import *

sys.path.append(".")

parser = ArgumentParser()
parser.add_argument("--input_dir", type=str, default="/workspace/input")
parser.add_argument("--output_dir", type=str, default="/workspace/input")
parser.add_argument("--stage",  type=int,
                    help="process stage, 0: stage0, extract topics and combine points. \
                         1: stage1, undisorted , bin,\
                         2: pcd, resave as xtreme format ")
parser.add_argument(
    "--model",
    type=str,
    choices=("dr1", "dr2", "pt1", "pt2", "pt3", "pt3_3", "pt3_4"),
    default=None,
)
parser.add_argument("--model_path", type=str, default="sensor_params.json")
parser.add_argument("--h264_topic", type=str, default=None)
parser.add_argument("--image_topic", type=str, default=None)
parser.add_argument("--zvision_topic", type=str, default=None)
parser.add_argument("--livox_topic", type=str, default=None)
parser.add_argument("--hasco_topic", type=str, default=None)
parser.add_argument("--split", type=bool, default=False)
parser.add_argument("--undistort", type=bool, default=False)
parser.add_argument("--focal_scale", type=float, default=1.0)
parser.add_argument("--view_seq", type=str, default="front,rear,left,right")
parser.add_argument("--verbose", type=bool, default=False)
parser.add_argument(
    "--start_time", type=int, help="Start time for extraction (in milliseconds)"
)
parser.add_argument(
    "--end_time", type=int, help="End time for extraction (in milliseconds)"
)
parser.add_argument(
    "--combine_lidars",
    type=bool,
    default=False,
    help="combine main and sub lidar points",
)
parser.add_argument("--zvision_pcd_topic", type=str, default=None)
parser.add_argument("--livox_pcd_topic", type=str, default=None)
parser.add_argument("--hasco_pcd_topic", type=str, default=None)
parser.add_argument("--hasco_4_lidar_pcd_topics", nargs="+")
parser.add_argument("--odometry_topic", type=str, default=None)
parser.add_argument("--divide", type=bool, default=False)
parser.add_argument("--workers", type=int, default=8)
args = parser.parse_args()


def get_sorted_mcaps(input_dir):
    if not os.path.exists(input_dir):
        print(f"{input_dir} does not exits")
        return []
    for root, dirs, files in os.walk(input_dir):
        index_order = []
        sorted_path = []
        target_files = []
        for file in files:
            if file.endswith("mcap"):
                # Try to extract numeric index from filename
                # Handle different filename patterns
                parts = file.split(".")[0].split("_")
                index = None

                # Try to find a numeric part in the filename
                for part in reversed(parts):
                    try:
                        index = int(part)
                        break
                    except ValueError:
                        continue

                # If no numeric index found, use 0 as default or filename hash
                if index is None:
                    # Use a hash of the filename to ensure consistent ordering
                    index = hash(file) % 1000000  # Keep it reasonable
                    print(f"Warning: No numeric index found in {file}, using hash-based index: {index}")

                index_order.append(index)
                target_files.append(file)

        # Sort files by their indices
        if target_files:
            sorted_pairs = sorted(zip(index_order, target_files))
            sorted_path = [Path(os.path.join(input_dir, file)) for _, file in sorted_pairs]
        else:
            sorted_path = []

        print(f"Found {len(sorted_path)} mcap files:")
        for path in sorted_path:
            print(f"  {path}")
        return sorted_path


def stage_0_process(mcap_files: list, output_dir: Path):
    print("[stage_0]: extract h264, lidars and combine main and sub lidars")

    model = parse_json_to_vehicleParam(args.model_path)
    # # Vehicle model
    # if args.model == "dr1":
    #     model = DR1
    # elif args.model == "dr2":
    #     model = DR2
    # elif args.model == "pt1":
    #     model = PT1
    # elif args.model == "pt2":
    #     model = PT2
    # elif args.model == "pt3":
    #     model = PT3
    # elif args.model == "pt3_3":
    #     model = PT3_3
    # elif args.model == "pt3_4":
    #     model = PT3_4
    # else:
    #     raise ValueError(f"Unknown model: {args.model}")
    # print(f"model: {args.model.upper()}")

    h264_raw_data, h264_timestamps, h264_proto_msgs_list = [], [], []
    print(" Extracting h264...")
    for mcap_file in mcap_files:
        # Prepare for extracting
        parser = McapParser(mcap_file)

        # List all topics
        topics = parser.topics()
        print(f"found topics:")
        for topic in topics:
            print(f" - {topic}")

        def safety_check(topic):
            if topic not in topics:
                raise ValueError(f"Topic not found in mcap file: {topic}")

        if args.image_topic is not None:
            safety_check(args.image_topic)
            print(f" - topic: {args.image_topic}")
            output_image = output_dir / "stage_0_mcap_content" / "image"
            output_image.mkdir(parents=True, exist_ok=True)
            count = parser.extract_image(
                output_image,
                topics=[args.image_topic],
                start_time=args.start_time,
                end_time=args.end_time,
            )
            print(f"   Extracted images: {count}")

        if args.h264_topic is not None:
            safety_check(args.h264_topic)
            print(f" - topic: {args.h264_topic}")
            print(f"processing {mcap_file}")
            h264_proto_msgs = parser.extract_h264_raw(topics=[args.h264_topic])
            h264_proto_msgs_list.append(h264_proto_msgs)

    if len(h264_proto_msgs_list) > 0:
        output_h264 = output_dir / "stage_0_mcap_content" / "h264"
        output_h264.mkdir(parents=True, exist_ok=True)
        view_seq = [View.from_str(name) for name in args.view_seq.split(",")]
        assert len(view_seq) == 4, "view_seq must contain 4 views"
        count, h264_timestamps = parser.decode_h264(
            output_dir=output_h264,
            proto_msg_list=h264_proto_msgs_list,
            model=model,
            split=args.split,
            undistort=args.undistort,
            focal_scale=args.focal_scale,
            view_seq=view_seq,
            start_time=args.start_time,
            end_time=args.end_time,
        )
        print(f"   Extracted frames: {count}")

    for mcap_file in mcap_files:
        # Prepare for extracting
        parser = McapParser(mcap_file)

        main_lidar_bin_path = None
        sub_lidar_bin_path = None
        main_lidar_timestamps = []
        sub_lidar_timestamps = []
        combine_timestamps = []

        # List all topics
        topics = parser.topics()
        print(f"found topics:")
        for topic in topics:
            print(f" - {topic}")

        print(f"extracing...")

        def safety_check(topic):
            if topic not in topics:
                raise ValueError(f"Topic not found in mcap file: {topic}")

        if args.zvision_topic is not None:
            safety_check(args.zvision_topic)
            print(f" - topic: {args.zvision_topic}")
            output_lidar = output_dir / "stage_0_mcap_content" / "zvision_lidar_bin"
            output_lidar.mkdir(parents=True, exist_ok=True)
            count, sub_lidar_timestamps = parser.extract_lidar(
                output_lidar,
                topics=[args.zvision_topic],
                start_time=args.start_time,
                end_time=args.end_time,
            )
            print(f"   Extracted bins: {count}")
            sub_lidar_bin_path = output_lidar

        if args.livox_topic is not None:
            safety_check(args.livox_topic)
            print(f" - topic: {args.livox_topic}")
            output_lidar = output_dir / "stage_0_mcap_content" / "livox_lidar_bin"
            output_lidar.mkdir(parents=True, exist_ok=True)
            count, main_lidar_timestamps = parser.extract_lidar(
                output_lidar,
                topics=[args.livox_topic],
                start_time=args.start_time,
                end_time=args.end_time,
            )
            print(f"   Extracted bins: {count}")
            main_lidar_bin_path = output_lidar

        if args.hasco_topic is not None:
            safety_check(args.hasco_topic)
            print(f" - topic: {args.hasco_topic}")
            output_lidar = output_dir / "stage_0_mcap_content" / "hasco_lidar_bin"
            output_lidar.mkdir(parents=True, exist_ok=True)
            count, sub_lidar_timestamps = parser.extract_lidar(
                output_lidar,
                topics=[args.hasco_topic],
                start_time=args.start_time,
                end_time=args.end_time,
            )
            print(f"   Extracted bins: {count}")
            sub_lidar_bin_path = output_lidar

        if args.combine_lidars:
            print(f"combining main and sub lidar...")
            combiner = Combiner(model)
            output_lidar = output_dir / "stage_0_mcap_content" / "combined_bin"
            output_lidar.mkdir(parents=True, exist_ok=True)
            matched_pairs = combiner.match_closest_timestamp(
                main_lidar_timestamps, sub_lidar_timestamps, max_time_difference=35
            )
            combiner.combine_main_sub_lidar(
                matched_pairs, main_lidar_bin_path, sub_lidar_bin_path, output_lidar
            )
            print(f"   Matched: {matched_pairs.shape}")
            combine_timestamps = np.array(
                matched_pairs).reshape(-1, 2)[:, 0].tolist()
            # print(combine_timestamps)

        if args.hasco_pcd_topic is not None:
            safety_check(args.hasco_pcd_topic)
            print(f" - topic: {args.hasco_pcd_topic}")
            output_lidar = output_dir / "stage_0_mcap_content" / "hasco_lidar_pcd"
            output_lidar.mkdir(parents=True, exist_ok=True)
            count, sub_lidar_timestamps = parser.extract_lidar(
                output_lidar,
                topics=[args.hasco_pcd_topic],
                start_time=args.start_time,
                end_time=args.end_time,
                save_pcd=True,
            )
            print(f"   Extracted pcds: {count}")
            sub_lidar_bin_path = output_lidar

        if args.livox_pcd_topic is not None:
            safety_check(args.livox_pcd_topic)
            print(f" - topic: {args.livox_pcd_topic}")
            output_lidar = output_dir / "stage_0_mcap_content" / "livox_lidar_pcd"
            output_lidar.mkdir(parents=True, exist_ok=True)
            count, sub_lidar_timestamps = parser.extract_lidar(
                output_lidar,
                topics=[args.livox_pcd_topic],
                start_time=args.start_time,
                end_time=args.end_time,
                save_pcd=True,
            )
            print(f"   Extracted pcds: {count}")
            sub_lidar_bin_path = output_lidar

        if args.zvision_pcd_topic is not None:
            safety_check(args.zvision_pcd_topic)
            print(f" - topic: {args.zvision_pcd_topic}")
            output_lidar = output_dir / "stage_0_mcap_content" / "zvision_lidar_pcd"
            output_lidar.mkdir(parents=True, exist_ok=True)
            count, sub_lidar_timestamps = parser.extract_lidar(
                output_lidar,
                topics=[args.zvision_pcd_topic],
                start_time=args.start_time,
                end_time=args.end_time,
                save_pcd=True,
            )
            print(f"   Extracted pcds: {count}")
            sub_lidar_bin_path = output_lidar

        if args.hasco_4_lidar_pcd_topics is not None:
            # safety_check(args.hasco_4_lidar_pcd_topics)
            print(f" - topic: {args.hasco_4_lidar_pcd_topics}")
            for topic in args.hasco_4_lidar_pcd_topics:
                output_lidar = output_dir / "stage_0_mcap_content" / topic[1:]
                output_lidar.mkdir(parents=True, exist_ok=True)
                count, sub_lidar_timestamps = parser.extract_lidar(
                    output_lidar,
                    topics=[topic],
                    start_time=args.start_time,
                    end_time=args.end_time,
                    save_pcd=True,
                )
                print(f"   Extracted pcds: {count}")
            # sub_lidar_bin_path = output_lidar

        if args.odometry_topic is not None:
            safety_check(args.odometry_topic)
            print(f" - topic: {args.odometry_topic}")
            output_odometry = output_dir / "stage_0_mcap_content" / "odometry"
            output_odometry.mkdir(parents=True, exist_ok=True)
            count, sub_lidar_timestamps = parser.extract_odometry(
                output_odometry,
                topics=[args.odometry_topic],
                start_time=args.start_time,
                end_time=args.end_time,
            )
            print(f"   Extracted odometry: {count}")


def stage_1_process(output_dir: Path):
    print("[stage_1]: matching h264 and combined points ...")
    stage_0_combine_lidar = output_dir / "stage_0_mcap_content" / "combined_bin"
    stage_0_h264 = output_dir / "stage_0_mcap_content" / "h264"

    points_timestamps = get_bin_names_from_folder(stage_0_combine_lidar)
    h264_timestamps = get_jpg_names_from_folder(stage_0_h264)

    matcher = Matcher()
    matched_pairs = matcher.match_timestamps(
        h264_timestamps, points_timestamps, max_time_difference=50
    )
    print(f"[stage_1]: matched {matched_pairs.shape} pairs")

    stage_1_folder = output_dir / "stage_1_matched"
    stage_1_folder.mkdir(parents=True, exist_ok=True)

    matched_pointcloud = stage_1_folder / "combined_bin"
    matched_pointcloud.mkdir(parents=True, exist_ok=True)

    matched_h264_0 = stage_1_folder / "h264"
    matched_h264_0.mkdir(parents=True, exist_ok=True)

    for pair in tqdm(matched_pairs):
        shutil.copy2(
            os.path.join(stage_0_combine_lidar, f"{pair[1]}.bin"),
            os.path.join(matched_pointcloud, f"{pair[1]}.bin"),
        )
        shutil.copy2(
            os.path.join(stage_0_h264, f"{pair[0]}.jpg"),
            os.path.join(matched_h264_0, f"{pair[1]}.jpg"),
        )
    print("[stage_1]: all done!")


def stage_2_process(output_dir: Path):
    print("[stage_2]: generating xtreme1 data ...")
    model = parse_json_to_vehicleParam(args.model_path)
    # # Vehicle model
    # if args.model == "dr1":
    #     model = DR1
    # elif args.model == "dr2":
    #     model = DR2
    # elif args.model == "pt1":
    #     model = PT1
    # elif args.model == "pt2":
    #     model = PT2
    # elif args.model == "pt3":
    #     model = PT3
    # elif args.model == "pt3_3":
    #     model = PT3_3
    # elif args.model == "pt3_4":
    #     model = PT3_4
    # else:
    #     raise ValueError(f"Unknown model: {args.model}")
    # print(f"model: {args.model.upper()}")

    stage_1_combine_lidar = output_dir / "stage_1_matched" / "combined_bin"
    stage_1_h264 = output_dir / "stage_1_matched" / "h264"

    stage_2_folder = output_dir / "stage_2_release"
    stage_2_folder.mkdir(parents=True, exist_ok=True)

    lidar_point_cloud_0 = stage_2_folder / "lidar_point_cloud_0"
    lidar_point_cloud_0.mkdir(parents=True, exist_ok=True)

    bin_to_pcd_patch(stage_1_combine_lidar, lidar_point_cloud_0)

    # process images
    camera_image_0 = stage_2_folder / "camera_image_0"
    camera_image_0.mkdir(parents=True, exist_ok=True)
    camera_image_1 = stage_2_folder / "camera_image_1"
    camera_image_1.mkdir(parents=True, exist_ok=True)
    camera_image_2 = stage_2_folder / "camera_image_2"
    camera_image_2.mkdir(parents=True, exist_ok=True)
    camera_image_3 = stage_2_folder / "camera_image_3"
    camera_image_3.mkdir(parents=True, exist_ok=True)

    view_seq = [View.from_str(name) for name in args.view_seq.split(",")]
    assert len(view_seq) == 4, "view_seq must contain 4 views"
    crop_and_undistort(stage_1_h264, stage_2_folder,
                       model, args.focal_scale, view_seq)

    # produce camera config
    camera_config = stage_2_folder / "camera_config"
    camera_config.mkdir(parents=True, exist_ok=True)

    file_names = get_jpg_names_from_folder(camera_image_0)
    params_dict = export_params_to_dict(model)
    json_string = json.dumps(params_dict, indent=4)

    for file_name in tqdm(file_names):
        with open(f"{camera_config}/{file_name}.json", "w+") as f:
            f.write(json_string)
    print("[stage_2]: all done!")


def stage_3_process(output_dir: Path):
    print("[stage_3]: matching h264 and pcds ...")
    stage_0_combine_lidar = output_dir / "stage_0_mcap_content" / "hasco_lidar_pcd"
    stage_0_h264 = output_dir / "stage_0_mcap_content" / "h264"

    check_path(stage_0_combine_lidar)
    check_path(stage_0_h264)

    points_timestamps = get_pcd_names_from_folder(stage_0_combine_lidar)
    print(
        f"[stage3]: get {len(points_timestamps)} samples from {stage_0_combine_lidar}"
    )
    h264_timestamps = get_jpg_names_from_folder(stage_0_h264)
    print(f"[stage3]: get {len(h264_timestamps)} samples from {stage_0_h264}")

    matcher = Matcher()
    matched_pairs = matcher.match_timestamps(
        h264_timestamps, points_timestamps, max_time_difference=50
    )
    print(f"[stage_3]: matched {matched_pairs.shape} pairs")

    stage_3_folder = output_dir / "stage_3_matched"
    stage_3_folder.mkdir(parents=True, exist_ok=True)
    matched_pointcloud_dir = stage_3_folder / "pcd"
    matched_pointcloud_dir.mkdir(parents=True, exist_ok=True)
    matched_h264_dir = stage_3_folder / "h264"
    matched_h264_dir.mkdir(parents=True, exist_ok=True)

    src_pointcloud_dirs, dst_pointcloud_dirs = [], []
    src_h264_dirs, dst_h264_dirs = [], []
    for pair in matched_pairs:
        # shutil.copy2(os.path.join(stage_0_combine_lidar, f'{pair[1]}.pcd'),
        #              os.path.join(matched_pointcloud_dir, f'{pair[1]}.pcd'))
        # shutil.copy2(os.path.join(stage_0_h264, f'{pair[0]}.jpg'),
        #              os.path.join(matched_h264_dir, f'{pair[1]}.jpg'))
        src_pointcloud_dirs.append(
            os.path.join(stage_0_combine_lidar, f"{pair[1]}.pcd")
        )
        dst_pointcloud_dirs.append(
            os.path.join(matched_pointcloud_dir, f"{pair[1]}.pcd")
        )
        src_h264_dirs.append(os.path.join(stage_0_h264, f"{pair[0]}.jpg"))
        dst_h264_dirs.append(os.path.join(matched_h264_dir, f"{pair[1]}.jpg"))

    print(f"[stage_3]: copying pointclouds: {matched_pairs.shape} pairs")
    copy_files_in_parallel(
        src_pointcloud_dirs,
        dst_pointcloud_dirs,
        max_workers=args.workers,
        description="copying pointclouds",
    )
    copy_files_in_parallel(
        src_h264_dirs,
        dst_h264_dirs,
        max_workers=args.workers,
        description="copying images",
    )
    print(f"[stage_3]: matched {len(matched_pairs)} pairs images and pcds")

    all_folders = [matched_pointcloud_dir, matched_h264_dir]
    if args.odometry_topic is not None:
        print("[stage_3]: matching odometry ...")
        stage_0_odometry = output_dir / "stage_0_mcap_content" / "odometry"
        check_path(stage_0_odometry)
        odometry_timestamps = get_names_from_folder_by_subfix(
            stage_0_odometry, "json")
        print(
            f"[stage3]: get {len(odometry_timestamps)} samples from {stage_0_odometry}"
        )

        matched_odometry_dir = stage_3_folder / "odometry"
        matched_odometry_dir.mkdir(parents=True, exist_ok=True)
        matched_lidar_timestamps = matched_pairs[:, 1]
        matched_odometry = matcher.match_timestamps(
            matched_lidar_timestamps, odometry_timestamps, max_time_difference=20
        )
        print(f"[stage_3]: matched {matched_odometry.shape} pairs odometry")

        src_odometry_dir, dst_odometry_dir = [], []
        for pair in tqdm(matched_odometry):
            # shutil.copy2(os.path.join(stage_0_odometry, f'{pair[1]}.json'),
            #              os.path.join(matched_odometry_dir, f'{pair[0]}.json'))
            src_odometry_dir.append(os.path.join(
                stage_0_odometry, f"{pair[1]}.json"))
            dst_odometry_dir.append(
                os.path.join(matched_odometry_dir, f"{pair[0]}.json")
            )
        copy_files_in_parallel(
            src_odometry_dir,
            dst_odometry_dir,
            max_workers=args.workers,
            description="copying odometries",
        )

        all_folders.append(matched_odometry_dir)

    if len(all_folders) >= 3:
        # 找出所有文件夹中同名的文件（忽略后缀名）
        print(f"[stage3]: start to remove uncommon files...")
        common_files = find_common_files(all_folders)
        print(f"[stage3]: find {len(common_files)} common files")

        # 删除每个文件夹中不同名的文件
        for folder in all_folders:
            delete_uncommon_files(folder, common_files)

        print("[stage_3]: remove uncommon files done!")

    print("[stage_3]: all done!")


def stage_4_process(output_dir: Path):
    print("[stage_4]: generating xtreme1 data ...")
    model = parse_json_to_vehicleParam(args.model_path)

    stage_3_pcd = output_dir / "stage_3_matched" / "pcd"
    stage_3_h264 = output_dir / "stage_3_matched" / "h264"
    check_path(stage_3_pcd)
    check_path(stage_3_h264)

    stage_4_folder = output_dir / "stage_4_release"
    stage_4_folder.mkdir(parents=True, exist_ok=True)

    # process images
    camera_image_0 = stage_4_folder / "camera_image_0"
    camera_image_0.mkdir(parents=True, exist_ok=True)
    camera_image_1 = stage_4_folder / "camera_image_1"
    camera_image_1.mkdir(parents=True, exist_ok=True)
    camera_image_2 = stage_4_folder / "camera_image_2"
    camera_image_2.mkdir(parents=True, exist_ok=True)
    camera_image_3 = stage_4_folder / "camera_image_3"
    camera_image_3.mkdir(parents=True, exist_ok=True)

    view_seq = [View.from_str(name) for name in args.view_seq.split(",")]
    assert len(view_seq) == 4, "view_seq must contain 4 views"
    crop_and_undistort(stage_3_h264, stage_4_folder,
                       model, args.focal_scale, view_seq)

    # produce camera config
    camera_config = stage_4_folder / "camera_config"
    camera_config.mkdir(parents=True, exist_ok=True)

    file_names = get_jpg_names_from_folder(camera_image_0)
    params_dict = export_params_to_dict(model)
    json_string = json.dumps(params_dict, indent=4)

    print("[stage_4]: generating camera_config...")
    for file_name in tqdm(file_names):
        with open(f"{camera_config}/{file_name}.json", "w+") as f:
            f.write(json_string)

    # copy pointclouds
    print("[stage_4]: copy pointclouds... ")
    lidar_point_cloud_0 = stage_4_folder / "lidar_point_cloud_0"
    lidar_point_cloud_0.mkdir(parents=True, exist_ok=True)
    shutil.copytree(stage_3_pcd, lidar_point_cloud_0, dirs_exist_ok=True)

    if args.odometry_topic is not None:
        print("[stage_4]: copy odometry... ")
        stage_3_odometry = output_dir / "stage_3_matched" / "odometry"
        check_path(stage_3_odometry)
        stage_4_odometry = stage_4_folder / "odometry"
        stage_4_odometry.mkdir(parents=True, exist_ok=True)
        shutil.copytree(stage_3_odometry, stage_4_odometry, dirs_exist_ok=True)

    print("[stage_4]: all done!")


def pack_dataset(output_dir: Path):
    print("[packing]: start to packing files...")
    print("[packing]: split files...")
    upload_folder = output_dir / "upload files"
    upload_folder.mkdir(parents=True, exist_ok=True)
    check_path(upload_folder)

    stage_4_folder = output_dir / "stage_4_release"
    camera_config = stage_4_folder / "camera_config"
    camera_image_0 = stage_4_folder / "camera_image_0"
    camera_image_1 = stage_4_folder / "camera_image_1"
    camera_image_2 = stage_4_folder / "camera_image_2"
    camera_image_3 = stage_4_folder / "camera_image_3"
    lidar_point_cloud_0 = stage_4_folder / "lidar_point_cloud_0"
    odometry = stage_4_folder / "odometry"

    file_names = get_names_from_folder_by_subfix(camera_config, "json")
    timestamp_splits = split_timestamp(file_names, 200, 30)
    for idx, split in enumerate(timestamp_splits):
        scene_folder = upload_folder / f"scene_{idx}"
        scene_folder.mkdir(parents=True, exist_ok=True)
        path_dict = create_scene_folder_with_odometry(scene_folder)

        src_camera_config_dirs, dst_camera_config_dirs = [], []
        src_camera_image_0_dirs, dst_camera_image_0_dirs = [], []
        src_camera_image_1_dirs, dst_camera_image_1_dirs = [], []
        src_camera_image_2_dirs, dst_camera_image_2_dirs = [], []
        src_camera_image_3_dirs, dst_camera_image_3_dirs = [], []
        src_lidar_point_cloud_0_dirs, dst_lidar_point_cloud_0_dirs = [], []
        src_odometry_dirs, dst_odometry_dirs = [], []

        print(f"[Divider]: processing scene_{idx}: ")
        for timestamp in split:

            # shutil.copy2(os.path.join(camera_config, f'{timestamp}.json'),
            #              os.path.join(path_dict['camera_config'], f'{timestamp}.json'))
            # shutil.copy2(os.path.join(camera_image_0, f'{timestamp}.jpg'),
            #              os.path.join(path_dict['camera_image_0'], f'{timestamp}.jpg'))
            # shutil.copy2(os.path.join(camera_image_1, f'{timestamp}.jpg'),
            #              os.path.join(path_dict['camera_image_1'], f'{timestamp}.jpg'))
            # shutil.copy2(os.path.join(camera_image_2, f'{timestamp}.jpg'),
            #              os.path.join(path_dict['camera_image_2'], f'{timestamp}.jpg'))
            # shutil.copy2(os.path.join(camera_image_3, f'{timestamp}.jpg'),
            #              os.path.join(path_dict['camera_image_3'], f'{timestamp}.jpg'))
            # shutil.copy2(os.path.join(lidar_point_cloud_0, f'{timestamp}.pcd'),
            #              os.path.join(path_dict['lidar_point_cloud_0'], f'{timestamp}.pcd'))
            # shutil.copy2(os.path.join(odometry, f'{timestamp}.json'),
            #              os.path.join(path_dict['odometry'], f'{timestamp}.json'))
            src_camera_config_dirs.append(
                os.path.join(camera_config, f"{timestamp}.json")
            )
            dst_camera_config_dirs.append(
                os.path.join(path_dict["camera_config"], f"{timestamp}.json")
            )

            src_camera_image_0_dirs.append(
                os.path.join(camera_image_0, f"{timestamp}.jpg")
            )
            dst_camera_image_0_dirs.append(
                os.path.join(path_dict["camera_image_0"], f"{timestamp}.jpg")
            )

            src_camera_image_1_dirs.append(
                os.path.join(camera_image_1, f"{timestamp}.jpg")
            )
            dst_camera_image_1_dirs.append(
                os.path.join(path_dict["camera_image_1"], f"{timestamp}.jpg")
            )

            src_camera_image_2_dirs.append(
                os.path.join(camera_image_2, f"{timestamp}.jpg")
            )
            dst_camera_image_2_dirs.append(
                os.path.join(path_dict["camera_image_2"], f"{timestamp}.jpg")
            )

            src_camera_image_3_dirs.append(
                os.path.join(camera_image_3, f"{timestamp}.jpg")
            )
            dst_camera_image_3_dirs.append(
                os.path.join(path_dict["camera_image_3"], f"{timestamp}.jpg")
            )

            src_lidar_point_cloud_0_dirs.append(
                os.path.join(lidar_point_cloud_0, f"{timestamp}.pcd")
            )
            dst_lidar_point_cloud_0_dirs.append(
                os.path.join(
                    path_dict["lidar_point_cloud_0"], f"{timestamp}.pcd")
            )

            src_odometry_dirs.append(
                os.path.join(odometry, f"{timestamp}.json"))
            dst_odometry_dirs.append(
                os.path.join(path_dict["odometry"], f"{timestamp}.json")
            )

        copy_files_in_parallel(
            src_camera_config_dirs,
            dst_camera_config_dirs,
            max_workers=args.workers,
            description="copying camera_config",
        )
        copy_files_in_parallel(
            src_camera_image_0_dirs,
            dst_camera_image_0_dirs,
            max_workers=args.workers,
            description="copying camera_image_0",
        )
        copy_files_in_parallel(
            src_camera_image_1_dirs,
            dst_camera_image_1_dirs,
            max_workers=args.workers,
            description="copying camera_image_1",
        )
        copy_files_in_parallel(
            src_camera_image_2_dirs,
            dst_camera_image_2_dirs,
            max_workers=args.workers,
            description="copying camera_image_2",
        )
        copy_files_in_parallel(
            src_camera_image_3_dirs,
            dst_camera_image_3_dirs,
            max_workers=args.workers,
            description="copying camera_image_3",
        )
        copy_files_in_parallel(
            src_lidar_point_cloud_0_dirs,
            dst_lidar_point_cloud_0_dirs,
            max_workers=args.workers,
            description="copying lidar_point_cloud_0",
        )
        copy_files_in_parallel(
            src_odometry_dirs,
            dst_odometry_dirs,
            max_workers=args.workers,
            description="copying odometry",
        )


if __name__ == "__main__":
    # Safety check
    if args.stage == 0:
        mcap_list = get_sorted_mcaps(args.input_dir)
        stage_0_process(mcap_list, Path(args.output_dir))
        print(f"[stage_0]: All done!\nProcessed mcap files: {len(mcap_list)}")
    elif args.stage == 1:
        stage_1_process(Path(args.output_dir))
    elif args.stage == 2:
        stage_2_process(Path(args.output_dir))
    elif args.stage == 3:
        stage_3_process(Path(args.output_dir))
    elif args.stage == 4:
        stage_4_process(Path(args.output_dir))
    elif args.divide:
        pack_dataset(Path(args.output_dir))

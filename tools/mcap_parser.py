import io
import os
from pathlib import Path
import av
import cv2
import json
import numpy as np
from mcap.reader import make_reader
from mcap_protobuf.decoder import DecoderFactory
from mcap_ros2.decoder import DecoderFactory as RosDecoderFactory

# , DR1, DR2, PT1, PT2, PT3_3, PT3_4
from tools.cam_params import View, VehicleParam, CamParam
from pypcd4 import PointCloud, Encoding


# 创建 ROS 数据类型到 NumPy 数据类型的映射字典
ROS_TO_NUMPY_DTYPE = {
    1: np.int8,
    2: np.uint8,
    3: np.int16,
    4: np.uint16,
    5: np.int32,
    6: np.uint32,
    7: np.float32,
    8: np.float64,
}

DTYPE_LENGTH = {1: 1, 2: 1, 3: 2, 4: 2, 5: 4, 6: 4, 7: 4, 8: 8}


class McapParser:
    def __init__(self, mcap_file):
        self.mcap_file = mcap_file
        f = open(mcap_file, "rb")
        self.reader = reader = make_reader(
            f,
            decoder_factories=[DecoderFactory(), RosDecoderFactory()],
            validate_crcs=False,
        )

    def statistics(self):
        return self.reader.get_summary().statistics

    def topics(self):
        summary = self.reader.get_summary()
        topics = [v.topic for v in summary.channels.values()]
        return topics

    def undistort(
        self,
        bgr: np.ndarray,
        focal_scale: float,
        param: CamParam,
    ):
        # 使用FOCAL_SCALE缩放焦距
        new_camera_matrix = param.cam_matrix.copy()
        new_camera_matrix[0][0] *= focal_scale
        new_camera_matrix[1][1] *= focal_scale

        mapx, mapy = cv2.fisheye.initUndistortRectifyMap(
            param.cam_matrix,
            param.dist_coeffs,
            np.eye(3),
            new_camera_matrix,
            (param.width, param.height),
            cv2.CV_32FC1,
        )
        bgr = cv2.resize(bgr, (param.width, param.height))
        bgr = cv2.remap(
            bgr,
            mapx,
            mapy,
            interpolation=cv2.INTER_LINEAR,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=(0, 0, 0),
        )
        return bgr

    def decode_pointcloud2(self, msg):
        """
        解析 PointCloud2 msg, 返回点云numpy数组
        """
        dtype_list = []
        current_offset = 0
        for field in msg.fields:
            dtype_list.append(
                (
                    field.name,
                    np.dtype(ROS_TO_NUMPY_DTYPE[field.datatype]).newbyteorder(
                        ">" if msg.is_bigendian else "<"
                    ),
                    field.count,
                )
            )
            current_offset = field.offset + \
                field.count * DTYPE_LENGTH[field.datatype]
        for padding in range(msg.point_step - current_offset):
            dtype_list.append((str(padding), np.uint8, 1))
        points = np.frombuffer(msg.data, dtype_list)
        x = points["x"].astype(np.float32).reshape(-1, 1) * 0.01
        y = points["y"].astype(np.float32).reshape(-1, 1) * 0.01
        z = points["z"].astype(np.float32).reshape(-1, 1) * 0.01
        intensity = points["intensity"].astype(np.float32).reshape(-1, 1)
        if "index" in points.dtype.names:
            index = points["index"].astype(np.float32).reshape(-1, 1)
            point_cloud = np.concatenate((x, y, z, intensity, index), axis=1)
        else:
            point_cloud = np.concatenate((x, y, z, intensity), axis=1)
        return point_cloud

    def extract_time_range(self, timestamp, start_time, end_time):
        return start_time <= timestamp <= end_time

    def extract_image(self, output_dir: Path, topics, start_time, end_time):
        counter = 0
        for _, _, _, proto_msg in self.reader.iter_decoded_messages(topics):
            frame_time = proto_msg.header.stamp
            message_time = int(frame_time.sec * 1e3 +
                               frame_time.nanosec // 1e6)
            if start_time is not None and end_time is not None:
                if not self.extract_time_range(message_time, start_time, end_time):
                    continue
            data = np.frombuffer(proto_msg.data, dtype=np.uint8)
            data = data.reshape((proto_msg.height, proto_msg.width, 1))
            bgr = cv2.cvtColor(data, cv2.COLOR_YUV2BGR_NV12)
            img_path = output_dir / f"{message_time}.jpg"
            cv2.imwrite(img_path.as_posix(), bgr)
            counter += 1
        return counter

    def extract_h264(
        self,
        output_dir: Path,
        topics,
        model: VehicleParam,
        split=False,
        undistort=False,
        focal_scale=1.0,
        view_seq=[View.FRONT, View.REAR, View.LEFT, View.RIGHT],
        start_time=None,
        end_time=None,
    ):
        # Collect H264 data and timestamps
        h264 = io.BytesIO()
        h264.write(model.h264_header)
        timestamps = []
        for _, _, _, proto_msg in self.reader.iter_decoded_messages(topics):
            frame_time = proto_msg.header.stamp
            message_time = int(frame_time.sec * 1e3 +
                               frame_time.nanosec // 1e6)
            # 在读取原始H.264数据和时间戳时choice range：
            if start_time is not None and end_time is not None:
                if not self.extract_time_range(message_time, start_time, end_time):
                    continue
            timestamps.append(message_time)
            h264.write(proto_msg.data)

        # Prepare output directories
        frame_dirs = [output_dir / "frames" / v.name for v in view_seq]
        [frame_dir.mkdir(parents=True, exist_ok=True)
         for frame_dir in frame_dirs]

        # Decode frames
        h264.seek(0)
        container = av.open(h264, format="h264", mode="r")
        for frame_idx, frame in enumerate(container.decode(video=0)):
            if frame_idx >= len(timestamps):
                print(
                    f"Warning: More frames than timestamps. Stopping at frame {frame_idx}"
                )
                break
            message_time = timestamps[frame_idx]
            bgr = cv2.cvtColor(frame.to_ndarray(
                format="rgb24"), cv2.COLOR_RGB2BGR)
            if split:
                h, w, _ = bgr.shape
                h_half, w_half = h // 2, w // 2
                origins = [
                    (0, 0),
                    (0 + w_half, 0),
                    (0, 0 + h_half),
                    (0 + w_half, 0 + h_half),
                ]
                for i, (x, y) in enumerate(origins):
                    param = model.cam_param(view_seq[i])
                    patch = bgr[y: y + h_half, x: x + w_half]
                    if undistort:
                        patch = self.undistort(patch, focal_scale, param)
                    img_path = frame_dirs[i] / f"{message_time}.jpg"
                    cv2.imwrite(img_path.as_posix(), patch)
            else:
                img_path = os.path.join(
                    output_dir, "frames", f"{message_time}.jpg")
                cv2.imwrite(img_path, bgr)

            if frame_idx % 100 == 0:
                print(f"Processed {frame_idx + 1} frames")

        print(f"Total frames processed: {frame_idx + 1}")
        print(f"Total timestamps: {len(timestamps)}")
        return frame_idx + 1, timestamps

    def extract_h264_raw(self, topics):
        # Collect H264 data and timestamps
        proto_msg_list = []
        for _, _, _, proto_msg in self.reader.iter_decoded_messages(topics):
            proto_msg_list.append(proto_msg)
        return proto_msg_list

    def decode_h264(
        self,
        output_dir: Path,
        proto_msg_list,
        model: VehicleParam,
        split=False,
        undistort=False,
        focal_scale=1.0,
        view_seq=[View.FRONT, View.REAR, View.LEFT, View.RIGHT],
        start_time=None,
        end_time=None,
    ):
        # Collect H264 data and timestamps
        h264 = io.BytesIO()

        # 1600x1280
        # h264.write(bytes([0, 0, 0, 1, 103, 100, 0, 51, 75, 10, 208, 12, 128, 80,
        #            208, 128, 0, 64, 0, 0, 15, 0, 0, 66, 0, 0, 0, 1, 104, 74, 227, 203]))
        # 1600x2100
        # h264.write(bytes([0, 0, 0, 1, 103, 100, 0, 51, 75, 10, 208, 12, 128, 33, 63,
        #            60, 32, 0, 16, 0, 0, 3, 3, 192, 0, 16, 128, 0, 0, 0, 1, 104, 74, 227, 203]))
        # 1600x1200
        # h264.write(
        #     b"\x00\x00\x00\x01gd\x003K\n\xd0\x0c\x80K\xd0\x80\x00@\x00\x00\x0f\x00\x00B\x00\x00\x00\x01hJ\xe3\xcb")

        timestamps = []
        while proto_msg_list:
            proto_msgs = proto_msg_list.pop(0)
            for proto_msg in proto_msgs:
                frame_time = proto_msg.header.stamp
                message_time = int(frame_time.sec * 1e3 +
                                   frame_time.nanosec // 1e6)
                # 在读取原始H.264数据和时间戳时choice range：
                if start_time is not None and end_time is not None:
                    if not self.extract_time_range(message_time, start_time, end_time):
                        continue
                timestamps.append(message_time)
                h264.write(proto_msg.data)

        # Prepare output directories
        frame_dir = output_dir / "frames"
        frame_dir.mkdir(parents=True, exist_ok=True)
        if split:
            frame_dirs = [output_dir / "frames" / v.name for v in view_seq]
            [frame_dir.mkdir(parents=True, exist_ok=True)
             for frame_dir in frame_dirs]

        # Decode frames
        h264.seek(0)

        # Validate H264 data before processing
        h264_data = h264.getvalue()
        if len(h264_data) == 0:
            print("Warning: No H264 data found in the stream")
            return 0, timestamps

        print(f"H264 data size: {len(h264_data)} bytes")

        try:
            container = av.open(h264, format="h264", mode="r")
        except Exception as e:
            print(f"Error opening H264 container: {e}")
            return 0, timestamps

        frame_idx = 0
        successful_frames = 0

        for packet_idx, packet in enumerate(container.demux()):
            try:
                frames = packet.decode()
                if len(frames) == 0:
                    print(f"Packet {packet_idx}: frame empty, skipping.")
                    continue

                if frame_idx >= len(timestamps):
                    print(f"Warning: More frames than timestamps. Stopping at frame {frame_idx}/{len(timestamps)}")
                    break

                message_time = timestamps[frame_idx]
                frame = frames[0]
                successful_frames += 1

            except av.error.InvalidDataError as e:
                print(f"Warning: Invalid data in packet {packet_idx}, skipping frame {frame_idx}: {e}")
                frame_idx += 1
                continue
            except Exception as e:
                print(f"Error decoding packet {packet_idx}, frame {frame_idx}: {e}")
                frame_idx += 1
                continue
            try:
                bgr = cv2.cvtColor(frame.to_ndarray(
                    format="rgb24"), cv2.COLOR_RGB2BGR)
                if bgr is None:
                    print(f"Warning: Failed to convert frame {frame_idx} to BGR")
                    frame_idx += 1
                    continue

                if split:
                    h, w, _ = bgr.shape
                    h_half, w_half = h // 2, w // 2
                    origins = [
                        (0, 0),
                        (0 + w_half, 0),
                        (0, 0 + h_half),
                        (0 + w_half, 0 + h_half),
                    ]
                    for i, (x, y) in enumerate(origins):
                        param = model.cam_param(view_seq[i])
                        patch = bgr[y: y + h_half, x: x + w_half]
                        if undistort:
                            patch = self.undistort(patch, focal_scale, param)
                        img_path = frame_dirs[i] / f"{message_time}.jpg"
                        cv2.imwrite(img_path.as_posix(), patch)
                else:
                    img_path = os.path.join(output_dir, f"{message_time}.jpg")
                    cv2.imwrite(img_path, bgr)

                if frame_idx % 100 == 0:
                    print(f"Processed {frame_idx + 1} frames")

            except Exception as e:
                print(f"Error processing frame {frame_idx}: {e}")

            frame_idx += 1

        print(f"Total frames processed: {successful_frames}")
        print(f"Total packets processed: {frame_idx}")
        print(f"Total timestamps: {len(timestamps)}")

        # Close container properly
        try:
            container.close()
        except:
            pass

        return successful_frames, timestamps

    def extract_lidar(
        self, output_dir: Path, topics, start_time, end_time, save_pcd=False
    ):
        counter = 0
        timestamps = []

        for _, _, _, proto_msg in self.reader.iter_decoded_messages(topics):
            frame_time = proto_msg.header.stamp
            message_time = int(frame_time.sec * 1e3 +
                               frame_time.nanosec // 1e6)
            if start_time is not None and end_time is not None:
                if not self.extract_time_range(message_time, start_time, end_time):
                    continue

            timestamps.append(message_time)
            n_points = proto_msg.width * proto_msg.height

            points = self.decode_pointcloud2(proto_msg)
            if save_pcd:
                if points.shape[-1] == 5:
                    output_path = output_dir / f"{message_time}.pcd"
                    pcd = PointCloud.from_xyzii_points(
                        points)  # 二进制文件包含 x, y, z, intensity
                    # 保存为 ASCII 格式的 PCD 文件
                    pcd.save(output_path, encoding=Encoding.ASCII)
                    fmt = "%f %f %f %.1f %f"  # intensity 保留一位小数shape
                else:
                    output_path = output_dir / f"{message_time}.pcd"
                    pcd = PointCloud.from_xyzi_points(points)
                    # 保存为 ASCII 格式的 PCD 文件
                    pcd.save(output_path, encoding=Encoding.ASCII)
                    fmt = "%f %f %.1f %f"

                # 保存点云数据，使用自定义的格式化字符串
                with open(output_path, "w") as f:
                    # 写入 PCD 头部信息
                    f.write(pcd.metadata.compose_header())
                    # 写入点云数据
                    np.savetxt(f, points, fmt=fmt)
            else:
                output_path = output_dir / f"{message_time}.bin"
                points.tofile(output_path)

            counter += 1
            if counter % 100 == 0:
                print(f" processing counts: {counter}")

        return counter, timestamps

    def extract_imu(self, output_dir: Path, topics, start_time, end_time):
        pass

    def extract_gnss(self, output_dir: Path, topics, start_time, end_time):
        pass

    def extract_gps(self, output_dir: Path, topics, start_time, end_time):
        pass

    def extract_odometry(self, output_dir: Path, topics, start_time, end_time):
        counter = 0
        timestamps = []

        for _, _, _, proto_msg in self.reader.iter_decoded_messages(topics):
            frame_time = proto_msg.header.stamp
            message_time = int(frame_time.sec * 1e3 +
                               frame_time.nanosec // 1e6)

            # 提取Odometry信息
            odom_info = {
                "timestamp": message_time,  # 时间戳
                "position": {
                    "x": proto_msg.pose.pose.position.x,
                    "y": proto_msg.pose.pose.position.y,
                    "z": proto_msg.pose.pose.position.z,
                },
                "orientation": {
                    "x": proto_msg.pose.pose.orientation.x,
                    "y": proto_msg.pose.pose.orientation.y,
                    "z": proto_msg.pose.pose.orientation.z,
                    "w": proto_msg.pose.pose.orientation.w,
                },
                "linear_velocity": {
                    "x": proto_msg.twist.twist.linear.x,
                    "y": proto_msg.twist.twist.linear.y,
                    "z": proto_msg.twist.twist.linear.z,
                },
                "angular_velocity": {
                    "x": proto_msg.twist.twist.angular.x,
                    "y": proto_msg.twist.twist.angular.y,
                    "z": proto_msg.twist.twist.angular.z,
                },
            }

            odometry_dir = output_dir / f"{message_time}.json"
            with open(odometry_dir, "w+") as f:
                json.dump(odom_info, f, indent=4)

            timestamps.append(message_time)
            counter += 1
            if counter % 100 == 0:
                print(f" processing counts: {counter}")

        return counter, timestamps

import os
import json
import matplotlib.pyplot as plt

# 定义文件夹路径
folder_path = "./odometry"  # 替换为你的 JSON 文件所在文件夹路径

# 初始化存储时间差的列表
time_differences = []

# 遍历文件夹中的所有文件
for filename in os.listdir(folder_path):
    if filename.endswith(".json"):  # 确保只处理 JSON 文件
        file_path = os.path.join(folder_path, filename)
        
        # 读取 JSON 文件
        with open(file_path, "r") as file:
            data = json.load(file)
        
        # 提取文件名中的时间戳（假设文件名是时间戳）
        try:
            file_timestamp = int(filename.split(".")[0])  # 去掉文件扩展名并转换为整数
        except ValueError:
            print(f"文件名 {filename} 不是有效的时间戳，跳过")
            continue
        
        # 提取 JSON 中的 timestamp
        json_timestamp = data.get("timestamp")
        
        # 计算时间差（假设时间戳单位是毫秒）
        print(json_timestamp, ":", file_timestamp)
        time_diff = abs(json_timestamp - file_timestamp)
        time_differences.append(time_diff)

# 绘制图表
plt.figure(figsize=(10, 6))
plt.plot(time_differences, marker="o", linestyle="-", color="b")
plt.title("Timestamp Differences Between JSON and Filename")
plt.xlabel("File Index")
plt.ylabel("Time Difference (ms)")
plt.grid(True)

# 保存图像到本地
output_image_path = "timestamp_differences.png"  # 保存路径和文件名
plt.savefig(output_image_path, dpi=300, bbox_inches="tight")  # 保存为 PNG 文件
print(f"图像已保存到 {output_image_path}")

# 关闭图像（避免内存泄漏）
plt.close()

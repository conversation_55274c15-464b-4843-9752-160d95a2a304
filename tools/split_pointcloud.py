import numpy as np
import pypcd4 as pypcd
from pypcd4 import PointCloud, Encoding


def split_pcd_by_index(input_path):
    # 读取包含自定义字段的PCD文件
    pc = pypcd.PointCloud.from_path(input_path)

    print(pc.pc_data)

    # 提取所有字段数据（转为结构化数组）
    data = pc.pc_data.view(np.dtype([
        ('x', np.float32), ('y', np.float32), ('z', np.float32),
        ('intensity', np.float32), ('index', np.float32)
    ])).reshape(-1)

    print(data)

    # 按index值分割点云
    index_1 = data[data['index'] == 1]
    index_2 = data[data['index'] == 2]
    index_3 = data[data['index'] == 3]
    index_4 = data[data['index'] == 4]

    print(f"Index 1 count: {len(index_1)}")
    print(f"Index 2 count: {len(index_2)}")
    print(f"Index 3 count: {len(index_3)}")
    print(f"Index 4 count: {len(index_4)}")

    # 转换为PyPCD点云对象并保存
    for idx, subset in enumerate([index_1, index_2, index_3, index_4], 1):
        # cloud = pypcd.PointCloud.from_array(subset)
        print(type(subset))
        print(subset)
        subset = subset.view(np.float32).reshape(-1, 5)
        cloud = pypcd.PointCloud.from_xyzii_points(subset)
        # cloud.save_pcd(f'index_{idx}_cloud.pcd', compression='ascii')

        cloud.save(f'index_{idx}_cloud.pcd', encoding=Encoding.ASCII)


if __name__ == "__main__":
    split_pcd_by_index("1745477956657.pcd")

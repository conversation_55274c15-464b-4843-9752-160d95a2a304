from dataclasses import dataclass
from enum import Enum

import numpy as np
import json
import os

H264_HEADERS = {
    "H264_HEADER_960": b"\x00\x00\x00\x01gd\x003K\n\xd0\x0c\x80K\xd0\x80\x00@\x00\x00\x0f\x00\x00B\x00\x00\x00\x01hJ\xe3\xcb",
    "H264_HEADER_720": b"\x00\x00\x00\x01gd\x003K\n\xd0\x0c\x80\xe7\xf3\xc2\x00\x00\x03\x00\x02\x00\x00\x03\x00y\x08\x00\x00\x00\x01hJ\xe3\xcb",
    "H264_HEADER_1536": b"\x00\x00\x00\x01gd\x003K\n\xd0\x0c\x80\xe7\xf3\xc2\x00\x00\x03\x00\x02\x00\x00\x03\x00y\x08\x00\x00\x00\x01hJ\xe3\xcb",
}
H264_HEADER_960 = b"\x00\x00\x00\x01gd\x003K\n\xd0\x0c\x80K\xd0\x80\x00@\x00\x00\x0f\x00\x00B\x00\x00\x00\x01hJ\xe3\xcb"
H264_HEADER_720 = b"\x00\x00\x00\x01gd\x003K\n\xd0\x0c\x80\xe7\xf3\xc2\x00\x00\x03\x00\x02\x00\x00\x03\x00y\x08\x00\x00\x00\x01hJ\xe3\xcb"


class View(Enum):
    FRONT = 1
    REAR = 2
    LEFT = 3
    RIGHT = 4

    @classmethod
    def from_str(cls, name: str):
        name = name.lower()
        if name == "front":
            return View.FRONT
        elif name == "rear":
            return View.REAR
        elif name == "left":
            return View.LEFT
        elif name == "right":
            return View.RIGHT
        else:
            raise ValueError(f"Invalid view name: {name}")


@dataclass
class CamParam:
    """相机解除畸变所需参数"""

    width: int
    height: int
    view: View
    cam_matrix: np.ndarray
    dist_coeffs: np.ndarray
    external: np.ndarray


@dataclass
class LidarParam:
    """主/副 lidar的外参"""

    trans_rot_main: list
    trans_pos_main: list
    trans_rot_sub: list
    trans_pos_sub: list
    lidar0_matrix: np.ndarray
    lidar1_matrix: np.ndarray
    lidar2_matrix: np.ndarray
    lidar3_matrix: np.ndarray


@dataclass
class VehicleParam:
    """车辆参数"""

    h264_header: bytes
    cam_front: CamParam
    cam_left: CamParam
    cam_rear: CamParam
    cam_right: CamParam
    lidar_extrinsics: LidarParam

    def cam_param(self, view: View) -> CamParam:
        if view == View.FRONT:
            return self.cam_front
        elif view == View.LEFT:
            return self.cam_left
        elif view == View.REAR:
            return self.cam_rear
        elif view == View.RIGHT:
            return self.cam_right


def calculate_camera_to_lidar_extrinsic(T_camera_vehicle, T_lidar_vehicle):
    """
    计算相机到激光雷达的外参。

    Args:
        T_camera_vehicle: 相机到车体的 4x4 变换矩阵。
        T_lidar_vehicle: 激光雷达到车体的 4x4 变换矩阵。

    Returns:
        T_camera_lidar: 相机到激光雷达的 4x4 变换矩阵。
        None: 如果输入矩阵的形状不正确。
    """

    if T_camera_vehicle.shape != (4, 4) or T_lidar_vehicle.shape != (4, 4):
        print("Error: Input matrices must be 4x4.")
        return None

    # 计算激光雷达到车体的逆变换矩阵
    T_vehicle_lidar = np.linalg.inv(T_lidar_vehicle)

    # 计算相机到激光雷达的变换矩阵
    T_camera_lidar = np.dot(T_vehicle_lidar, T_camera_vehicle)

    return T_camera_lidar


def calculate_lidar_to_camera_extrinsic(T_camera_vehicle, T_lidar_vehicle):
    """
    计算激光雷达到相机的外参。

    Args:
        T_camera_vehicle: 相机到车体的 4x4 变换矩阵。
        T_lidar_vehicle: 激光雷达到车体的 4x4 变换矩阵。

    Returns:
        T_lidar_camera: 激光雷达到相机的 4x4 变换矩阵。
        None: 如果输入矩阵的形状不正确。
    """

    if T_camera_vehicle.shape != (4, 4) or T_lidar_vehicle.shape != (4, 4):
        print("Error: Input matrices must be 4x4.")
        return None

    # 计算相机到车体的逆变换矩阵
    T_vehicle_camera = np.linalg.inv(T_camera_vehicle)

    # 计算激光雷达到相机的变换矩阵
    T_lidar_camera = np.dot(T_vehicle_camera, T_lidar_vehicle)

    return T_lidar_camera


def create_transformation_matrix(rx, ry, rz, tx, ty, tz):
    Rx = np.array(
        [[1, 0, 0], [0, np.cos(rx), -np.sin(rx)], [0, np.sin(rx), np.cos(rx)]]
    )
    Ry = np.array(
        [[np.cos(ry), 0, np.sin(ry)], [0, 1, 0], [-np.sin(ry), 0, np.cos(ry)]]
    )
    Rz = np.array(
        [[np.cos(rz), -np.sin(rz), 0], [np.sin(rz), np.cos(rz), 0], [0, 0, 1]]
    )
    R = np.dot(np.dot(Rx, Ry), Rz)
    T = np.eye(4)
    T[:3, :3] = R
    T[:3, 3] = [tx, ty, tz]
    return T


def parse_json_to_vehicleParam(json_path):
    if not os.path.exists(json_path):
        print(f"{json_path} does not exits.")
        return

    json_result = None
    with open(json_path, "r") as f:
        json_result = json.load(f)
    print(json_result)

    veh_param = VehicleParam(
        h264_header=H264_HEADERS[json_result["h264_header"]],
        cam_front=CamParam(
            width=json_result["cam_front"]["width"],
            height=json_result["cam_front"]["height"],
            view=View.FRONT,
            cam_matrix=np.array(json_result["cam_front"]["cam_matrix"]).reshape(3, 3),
            dist_coeffs=np.array(json_result["cam_front"]["dist_coeffs"]).reshape(4, 1),
            # external=calculate_lidar_to_camera_extrinsic(
            #     np.array(json_result['cam_front']['external']).reshape(4, 4),
            #     np.array(json_result['lidar_extrinsics']['lidar0_extrinsics']).reshape(4, 4))
            external=np.array(json_result["cam_front"]["external"]).reshape(4, 4),
        ),
        cam_left=CamParam(
            width=json_result["cam_left"]["width"],
            height=json_result["cam_left"]["height"],
            view=View.LEFT,
            cam_matrix=np.array(json_result["cam_left"]["cam_matrix"]).reshape(3, 3),
            dist_coeffs=np.array(json_result["cam_left"]["dist_coeffs"]).reshape(4, 1),
            # external=calculate_lidar_to_camera_extrinsic(
            #     np.array(json_result['cam_left']['external']).reshape(4, 4),
            #     np.array(json_result['lidar_extrinsics']['lidar0_extrinsics']).reshape(4, 4))
            external=np.array(json_result["cam_left"]["external"]).reshape(4, 4),
        ),
        cam_rear=CamParam(
            width=json_result["cam_rear"]["width"],
            height=json_result["cam_rear"]["height"],
            view=View.REAR,
            cam_matrix=np.array(json_result["cam_rear"]["cam_matrix"]).reshape(3, 3),
            dist_coeffs=np.array(json_result["cam_rear"]["dist_coeffs"]).reshape(4, 1),
            # external=calculate_lidar_to_camera_extrinsic(
            #     np.array(json_result['cam_rear']['external']).reshape(4, 4),
            #     np.array(json_result['lidar_extrinsics']['lidar0_extrinsics']).reshape(4, 4))
            external=np.array(json_result["cam_rear"]["external"]).reshape(4, 4),
        ),
        cam_right=CamParam(
            width=json_result["cam_right"]["width"],
            height=json_result["cam_right"]["height"],
            view=View.RIGHT,
            cam_matrix=np.array(json_result["cam_right"]["cam_matrix"]).reshape(3, 3),
            dist_coeffs=np.array(json_result["cam_right"]["dist_coeffs"]).reshape(4, 1),
            # external=calculate_lidar_to_camera_extrinsic(
            #     np.array(json_result['cam_right']['external']).reshape(4, 4),
            #     np.array(json_result['lidar_extrinsics']['lidar0_extrinsics']).reshape(4, 4))
            external=np.array(json_result["cam_right"]["external"]).reshape(4, 4),
        ),
        lidar_extrinsics=LidarParam(
            trans_rot_main=json_result["lidar_extrinsics"]["trans_rot_main"],
            trans_pos_main=json_result["lidar_extrinsics"]["trans_pos_main"],
            trans_rot_sub=json_result["lidar_extrinsics"]["trans_rot_sub"],
            trans_pos_sub=json_result["lidar_extrinsics"]["trans_pos_sub"],
            lidar0_matrix=np.array(
                json_result["lidar_extrinsics"]["lidar0_extrinsics"]
            ).reshape(4, 4),
            lidar1_matrix=np.array(
                json_result["lidar_extrinsics"]["lidar1_extrinsics"]
            ).reshape(4, 4),
            lidar2_matrix=np.array(
                json_result["lidar_extrinsics"]["lidar2_extrinsics"]
            ).reshape(4, 4),
            lidar3_matrix=np.array(
                json_result["lidar_extrinsics"]["lidar3_extrinsics"]
            ).reshape(4, 4),
        ),
    )
    return veh_param

    # todo: update all lidars extrinsics


"""

DR2 = VehicleParam(
    h264_header=H264_HEADER_720,
    cam_front=CamParam(
        width=1280,
        height=720,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [4.5885340000000002e02, 0.0, 6.4092800000000000e02],
                [0.0, 4.5905570000000000e02, 3.6037779999999998e02],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [6.0433526872810159e-02],
                [-2.9349126468851765e-03],
                [-1.1711074473183742e-02],
                [5.0380269561262842e-03],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=720,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [4.6217550000000000e02, 0.0, 6.4618010000000004e02],
                [0.0, 4.6257060000000001e02, 3.5722840000000002e02],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [8.6763190147438177e-02],
                [-1.0167614314102323e-01],
                [1.4688386132614725e-01],
                [-8.3053693657028388e-02],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=720,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [4.5891520000000003e02, 0.0, 6.3972500000000002e02],
                [0.0, 4.5840879999999999e02, 3.6073770000000002e02],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [6.8398439668715533e-02],
                [-1.2874202322800798e-02],
                [-3.0195408133019577e-03],
                [8.5862935575176037e-04],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=720,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [4.5420190000000002e02, 0.0, 6.3890350000000001e02],
                [0.0, 4.5411559999999997e02, 3.6013339999999999e02],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [0.07719455644120411],
                [-0.027392535256643338],
                [0.01324399041344262],
                [-0.004528966988899547],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0, 0, 0],
        trans_pos_sub=[0, 0, 0],
    )


)

DR1 = VehicleParam(
    h264_header=H264_HEADER_720,
    cam_front=CamParam(
        width=1280,
        height=720,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [449.4956822833007, 0.0, 637.6616967583534],
                [0.0, 447.8898687477129, 362.84392155935643],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [0.07185728815905178],
                [0.029654405604421535],
                [-0.05565972443736425],
                [0.019397199550540273],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=720,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [465.38452034856374, 0.0, 639.99115164009663],
                [0.0, 464.92451398246658, 356.83973832908214],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [0.078079479696475926],
                [-0.065373921249696640],
                [0.055688987972686822],
                [-0.020469437839735254],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=720,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [464.68627226940544, 0.0, 634.9266263309413],
                [0.0, 464.2242556209053, 364.65752226019055],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [0.06771846486232894],
                [-0.01217056139818844],
                [-0.008828776908154931],
                [0.0035984360889759317],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=720,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [456.89805827408236, 0.0, 641.976978147716],
                [0.0, 456.4782993911796, 365.6999721384337],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [
                [0.07598303802401958],
                [-0.020682927849142538],
                [0.0022815961261042805],
                [0.0003205156624013231],
            ]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0, 0, 0],
        trans_pos_sub=[0, 0, 0],
    )
)

PT1 = VehicleParam(
    h264_header=H264_HEADER_960,
    cam_front=CamParam(
        width=1280,
        height=960,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [314.77945619, 0.0, 635.83577798],
                [0.0, 314.72257393, 481.54928495],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.10693257], [-0.0202789], [0.00736614], [-0.00196957]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=960,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [315.53842518, 0.0, 636.53100222],
                [0.0, 315.38484746, 484.09815942],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.10711764], [-0.01994049], [0.00672056], [-0.00171661]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=960,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [318.00921885, 0.0, 638.03452719],
                [0.0, 318.40278818, 484.30013594],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.10538628], [-0.01860066], [0.00652579], [-0.00177778]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=960,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [315.77974317, 0.0, 639.74290396],
                [0.0, 315.76843931, 486.08028482],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.10376918], [-0.01473592], [0.00310979], [-0.000901]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0, 0, 0],
        trans_pos_sub=[0, 0, 0],
    )
)

PT2 = VehicleParam(
    h264_header=H264_HEADER_960,
    cam_front=CamParam(
        width=1280,
        height=960,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [317.4186315896, 0.0, 640.5392951553],
                [0.0, 317.1193812248, 480.0687777856],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1046931738], [-0.0190349181], [0.0064423678], [-0.0017037993]]),
        external=np.array(
            [
                [-1.0584748350083828e-02, -9.9993467330932617e-01, -
                    4.3175145983695984e-03, 2.1761400000000000e-01],
                [1.5538078732788563e-02, 4.1527608409523964e-03, -
                    9.9987065792083740e-01, -1.5137513000000001e-01],
                [9.9982327222824097e-01, -1.0650465264916420e-02,
                    1.5493107959628105e-02, -1.4813509000000000e-01],
                [0., 0., 0., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=960,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [316.1147350025, 0.0, 641.0071827655],
                [0.0, 315.8741060302, 482.2920907941],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1034961147], [-0.0135177473], [0.0023927341], [-0.0006910909]]),
        external=np.array(
            [
                [9.9994766000000004e-01, -8.7108300000000001e-03,
                    5.3710199999999997e-03, 5.3377360000000001e-02],
                [5.2608100000000001e-03, -1.2625560000000000e-02, -
                    9.9990643000000003e-01, -1.4173606000000000e-01],
                [8.7778200000000002e-03, 9.9988236000000001e-01, -
                    1.2579070000000000e-02, -3.1037621000000002e-01],
                [0., 0., 0., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=960,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [315.1401925287, 0.0,  639.9570658201],
                [0.0, 314.9768811269, 483.6058503674],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1055543407], [-0.0171198212], [0.004820348], [-0.0012956453]]
        ),
        external=np.array(
            [
                [2.2454260000000001e-02, 9.9974644000000001e-01,
                    1.6917300000000000e-03, 1.0596470000000000e-02],
                [-1.9325747000000001e-01, 6.0007900000000002e-03, -
                    9.8112975000000005e-01, -6.4824065000000001e-01],
                [-9.8089110000000001e-01, 2.1703600000000001e-02,
                    1.9334321000000000e-01, -7.6455230000000004e-01],
                [0., 0., 0., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=960,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [315.8588118654, 0.0, 641.1418128721],
                [0.0, 315.6075995492, 482.6690846594],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.10760638], [-0.0199752867], [0.0069748633], [-0.0018275383]]),
        external=np.array(
            [
                [-9.9965138000000001e-01, 6.8907700000000001e-03,
                    2.5488020000000000e-02, -5.5087490000000002e-02],
                [-2.5463370000000000e-02, 3.6414900000000002e-03, -
                 9.9966911000000004e-01, -1.4225228000000000e-01],
                [-6.9813000000000002e-03, -9.9996965000000004e-01, -
                 3.4647600000000001e-03, -2.9992202000000001e-01],
                [0., 0., 0., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0.74, 0.45, 2.46],
        trans_pos_sub=[0.00, 0.00, -0.05],
    )
)

PT3 = VehicleParam(
    h264_header=H264_HEADER_960,
    cam_front=CamParam(
        width=1280,
        height=960,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [317.0579488896, 0.0, 642.9171410523],
                [0.0, 317.232405474, 484.0132579236],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1036238313], [-0.0178258451], [0.0054646701], [-0.0014457918]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=960,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [315.7884465247, 0.0, 640.0654679973],
                [0.0, 315.4904825859, 486.1963913584],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1027947039], [-0.0132958312], [0.0023788517], [-0.0007480999]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=960,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [317.5137954932, 0.0, 640.475399799],
                [0.0, 317.3309420776, 482.0591869096],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1065560438], [-0.0183021343], [0.0056881941], [-0.0014496954]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=960,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [317.8643551623, 0.0, 644.5776063815],
                [0.0, 318.0711141961, 484.2248594587],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1035474298], [-0.0148308184], [0.003551708], [-0.0009926194]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0, 0, 0],
        trans_pos_sub=[0, 0, 0],
    )
)

PT3_3 = VehicleParam(
    h264_header=H264_HEADER_960,
    cam_front=CamParam(
        width=1280,
        height=960,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [316.5425037349, 0.0, 635.6176748643],
                [0.0, 316.6554250827, 484.085740936],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1048318354], [-0.0164395436], [0.0046584071], [-0.0012803785]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=960,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [315.4531057893, 0.0, 640.2841472812],
                [0.0, 315.7075502989, 482.2495789748],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.103576639], [-0.0168456309], [0.0051928013], [-0.0014329352]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=960,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [316.2215694568, 0.0, 635.345259747],
                [0.0, 316.0382540334, 485.3581984534],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1045678979], [-0.0153633307], [0.0040275044], [-0.001191542]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=960,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [315.2466558611, 0.0, 639.9740487925],
                [0.0, 315.4481055156, 484.4360300639],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1043637544], [-0.0151616791], [0.0038725414], [-0.0011251378]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0, 0, 0],
        trans_pos_sub=[0, 0, 0],
    )
)

PT3_4 = VehicleParam(
    h264_header=H264_HEADER_960,
    cam_front=CamParam(
        width=1280,
        height=960,
        view=View.LEFT,
        cam_matrix=np.array(
            [
                [315.3868379108, 0.0, 641.1582352509],
                [0.0, 315.6867711402, 484.085740936],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1048318354], [-0.0164395436], [0.0046584071], [-0.0012803785]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_left=CamParam(
        width=1280,
        height=960,
        view=View.FRONT,
        cam_matrix=np.array(
            [
                [315.4531057893, 0.0, 640.2841472812],
                [0.0, 315.7075502989, 482.2495789748],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.103576639], [-0.0168456309], [0.0051928013], [-0.0014329352]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_rear=CamParam(
        width=1280,
        height=960,
        view=View.REAR,
        cam_matrix=np.array(
            [
                [316.2215694568, 0.0, 635.345259747],
                [0.0, 316.0382540334, 485.3581984534],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1045678979], [-0.0153633307], [0.0040275044], [-0.001191542]]
        ),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    cam_right=CamParam(
        width=1280,
        height=960,
        view=View.RIGHT,
        cam_matrix=np.array(
            [
                [315.2466558611, 0.0, 639.9740487925],
                [0.0, 315.4481055156, 484.4360300639],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeffs=np.array(
            [[0.1043637544], [-0.0151616791], [0.0038725414], [-0.0011251378]]),
        external=np.array(
            [
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.],
                [1., 1., 1., 1.]
            ]
        )
    ),
    lidar_extrinsics=LidarParam(
        trans_rot_main=[0, 0, 0],
        trans_pos_main=[0, 0, 0],
        trans_rot_sub=[0, 0, 0],
        trans_pos_sub=[0, 0, 0],
    )
)
"""

if __name__ == "__main__":
    parse_json_to_vehicleParam("param.json")

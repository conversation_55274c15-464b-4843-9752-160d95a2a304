import os.path
import glob
import cv2
import io
import av
from tqdm import *
import numpy as np
from pathlib import Path

from mcap_protobuf.decoder import DecoderFactory
from mcap_ros2.decoder import DecoderFactory as RosDecoderFactory
from mcap.reader import make_reader

from tools.cam_params import View, VehicleParam, CamParam, LidarParam
from utils.utils import (
    get_bin_names_from_folder,
    get_jpg_names_from_folder,
    pos_trans_by_matrix,
    get_names_from_folder_by_subfix,
)


class Combiner:

    def __init__(self, model: VehicleParam) -> None:

        self.trans_rot_main = model.lidar_extrinsics.trans_rot_main
        self.trans_pos_main = model.lidar_extrinsics.trans_pos_main
        self.trans_rot_sub = model.lidar_extrinsics.trans_rot_sub
        self.trans_pos_sub = model.lidar_extrinsics.trans_pos_sub

    def match_closest_timestamp(
        self, main_timestamps, sub_timestamps, max_time_difference=0
    ):
        main_array = np.array(main_timestamps)
        sub_array = np.array(sub_timestamps)
        distance_matrix = np.abs(main_array[:, np.newaxis] - sub_array)
        min_index = np.argmin(distance_matrix, axis=1)
        closest_pairs = [
            (main_array[i], sub_array[min_index[i]]) for i in range(len(main_array))
        ]
        min_distances = distance_matrix[np.arange(len(main_array)), min_index]
        closest_pairs = np.array(closest_pairs)
        min_distances = np.array(min_distances)
        zero_difference_path = closest_pairs[
            np.where(min_distances <= max_time_difference)
        ]
        return zero_difference_path

    def combine_main_sub_lidar(
        self, matched_pairs, main_lidar_folder, sub_lidar_folder, output_folder
    ):
        for pair in matched_pairs:
            if pair[0] - pair[1] != 0:
                print("get mismatch", pair)

        for pair in tqdm(matched_pairs):
            # 1.load data
            main_lidar_path = os.path.join(main_lidar_folder, f"{pair[0]}.bin")
            sub_lidar_path = os.path.join(sub_lidar_folder, f"{pair[0]}.bin")
            points_xyzi_main = np.fromfile(main_lidar_path, dtype=np.float32).reshape(
                -1, 4
            )
            points_xyzi_sub = np.fromfile(sub_lidar_path, dtype=np.float32).reshape(
                -1, 4
            )

            # 2. R T transpose
            points_main = points_xyzi_main[:, 0:3]  # get only x,y,z
            points_sub = points_xyzi_sub[:, 0:3]  # get only x,y,z
            points_new_main = pos_trans_by_matrix(
                self.trans_rot_main, self.trans_pos_main, points_main
            )
            points_new_sub = pos_trans_by_matrix(
                self.trans_rot_sub, self.trans_pos_sub, points_sub
            )

            # 3. combine two point cloud
            points_xyzi_main[:, 0:3] = points_new_main
            points_xyzi_sub[:, 0:3] = points_new_sub
            points_out = np.vstack((points_xyzi_main, points_xyzi_sub))
            points_out.tofile(os.path.join(output_folder, f"{pair[0]}.bin"))


class Matcher:
    def __init__(self) -> None:
        pass

    def match_timestamps(self, timestamp1, timestamp2, max_time_difference=0):
        main_array = np.array(timestamp1)
        sub_array = np.array(timestamp2)
        distance_matrix = np.abs(main_array[:, np.newaxis] - sub_array)
        min_index = np.argmin(distance_matrix, axis=1)
        closest_pairs = [
            (main_array[i], sub_array[min_index[i]]) for i in range(len(main_array))
        ]
        min_distances = distance_matrix[np.arange(len(main_array)), min_index]
        closest_pairs = np.array(closest_pairs)
        min_distances = np.array(min_distances)
        zero_difference_path = closest_pairs[
            np.where(min_distances <= max_time_difference)
        ]
        return zero_difference_path

    def get_closest_timestamp_pairs(self, timestamp1, timestamp2):
        main_array = np.array(timestamp1)
        sub_array = np.array(timestamp2)
        distance_matrix = np.abs(main_array[:, np.newaxis] - sub_array)
        min_index = np.argmin(distance_matrix, axis=1)
        closest_pairs = [
            (main_array[i], sub_array[min_index[i]]) for i in range(len(main_array))
        ]
        return closest_pairs


if __name__ == "__main__":
    import os
    import pathlib
    import shutil

    # lidar_folder = "/mnt/mnt108/XCZN_Public/Perception_Group/00_datasets/01_XCZN/DR3-PT2/20240918_175710/output/20240918_175710"
    # h264_folder = "/mnt/mnt108/XCZN_Public/Perception_Group/00_datasets/01_XCZN/DR3-PT2/20240918_175710/output/20240918_175710/h264/clear_5choice1"
    # output_dir = Path("/mnt/mnt108/XCZN_Public/Perception_Group/00_datasets/01_XCZN/DR3-PT2/20240918_175710/output/release")

    # points_timestamps = get_bin_names_from_folder(lidar_folder)
    # h264_timestamps = get_jpg_names_from_folder(h264_folder)

    # matcher = Matcher()
    # matched_pairs = matcher.match_timestamps(h264_timestamps, points_timestamps, max_time_difference=50)
    # resave_pointcloud = output_dir /"matched"/"points"
    # resave_pointcloud.mkdir(parents=True, exist_ok=True)

    # resave_image = output_dir /"matched"/"images"
    # resave_image.mkdir(parents=True, exist_ok=True)

    # for pair in tqdm(matched_pairs):
    #     shutil.copy2(os.path.join(output_dir, "combine_bin", f'{pair[0]}.bin'),
    #                 os.path.join(resave_pointcloud, f'{pair[0]}.bin'))
    #     shutil.copy2(os.path.join(output_dir, "h264", "frames", f'{pair[1]}.jpg'),
    #                 os.path.join(resave_image, f'{pair[0]}.jpg'))

    lidar_folder = "/mnt/mnt108/XCZN_Public/GaoSong/01_datasets/2024-12-31/165_brainnode_data_20241231_154207/output2/stage_3_matched/pcd"
    odometry_folder = "/mnt/mnt108/XCZN_Public/GaoSong/01_datasets/2024-12-31/165_brainnode_data_20241231_154207/output2/stage_0_mcap_content/odometry"

    points_timestamps = get_names_from_folder_by_subfix(lidar_folder, "pcd")
    odometry_timestamps = get_names_from_folder_by_subfix(odometry_folder, "json")

    matcher = Matcher()
    matched_pairs = matcher.match_timestamps(
        points_timestamps, odometry_timestamps, max_time_difference=20
    )

    # matched_pairs = matcher.get_closest_timestamp_pairs(
    #     points_timestamps, odometry_timestamps)

    matched_pairs = np.array(matched_pairs)
    print(matched_pairs)
    print(matched_pairs.shape)

    odo_np = matched_pairs[:, 0]
    pcd_np = matched_pairs[:, 1]

    diff = matched_pairs[np.where((odo_np - pcd_np) > 0)]

    err = odo_np - pcd_np
    # print(err[np.where(np.abs(err) > 0)])
    print(matched_pairs[np.where(np.abs(err) > 0)])

    # print(diff)

    # print(diff[0].shape)
    # print(len(points_timestamps))
